<?php
// 云快充
namespace App\Console\Commands;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Request\YKC as YKCRequest;
use Throwable;


class PullForYKCDevice extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:ykc_device';
    protected $name      = 'pull oil for ykc_device';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for ykc_device';

    protected $nameAbbreviation = 'ykc';

    protected static $connectorStatusMapping = [
        0   => 10,
        1   => 20,
        2   => 30,
        3   => 40,
        4   => 50,
        255 => 60,
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $redis = app('redis');
        $stationIds = $redis->hkeys(self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation);
        $totalPage = ceil(count($stationIds) / 100);
        $page = 0;
        do {
            try {
                $data = YKCRequest::handle("query_station_status", [
                    "StationIDs" => array_values(
                        array_slice(
                            $stationIds,
                            ($page - 1) * 100,
                            100
                        )
                    ),
                ]);
                $updateData = [];
                foreach ($data['parsedData']['StationStatusInfos'] as $v) {
                    foreach ($v['ConnectorStatusInfos'] as $cv) {
                        $updateData[] = [
                            'station_id'           => $v['StationID'],
                            'connector_id'         => $cv['ConnectorID'],
                            'charging_status'      => $cv['Status'],
                            'parking_space_status' => $cv['ParkStatus'] ?? 0,
                            'lock_status'          => $cv['LockStatus'] ?? 0,
                        ];
                    }
                }
                try {
                    FOSS_STATIONRequest::handle("v1/charge/pushChargingStatus", [
                        'supplier_code' => $this->platformCode,
                        'data'          => $updateData,
                    ]);
                } catch (Throwable $throwable) {
                    Log::handle("Push station device failed", [
                        'exception'   => $throwable,
                        'initData'    => $data,
                        'stationData' => $updateData,
                    ], "云快充", "oil_station_data", "error");
                }
                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the site device failed", [
                    'exception'   => $throwable,
                    "station_ids" => array_values(
                        array_slice(
                            $stationIds,
                            ($page - 1) * 100,
                            100
                        )
                    ),
                ], "云快充", "oil_station_data", "error");
            }
        } while ($page < $totalPage);
    }

    public static function getStatusMapping(): array
    {
        return self::$connectorStatusMapping;
    }
}
