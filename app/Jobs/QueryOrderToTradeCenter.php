<?php


namespace App\Jobs;


use App\Models\Logic\QueryOrderToTradeCenter\DH;
use App\Models\Logic\QueryOrderToTradeCenter\HBKJ;
use App\Models\Logic\QueryOrderToTradeCenter\SAIC;
use App\Models\Logic\QueryOrderToTradeCenter\SQ;
use App\Models\Logic\QueryOrderToTradeCenter\SQZL;
use App\Models\Logic\QueryOrderToTradeCenter\SQZSH;
use App\Models\Logic\QueryOrderToTradeCenter\XMSK;
use App\Models\Logic\QueryOrderToTradeCenter\YC;
use App\Models\Logic\QueryOrderToTradeCenter\YKC;
use Exception;
use Throwable;

class QueryOrderToTradeCenter extends BasicJob
{
    private $args;
    private $handleMapping = [
        'yc'    => YC::class,
        'sq'    => SQ::class,
        'saic'  => SAIC::class,
        'sqzl'  => SQZL::class,
        'sqZsh' => SQZSH::class,
        'dh'    => DH::class,
        'xmsk'  => XMSK::class,
        'hbkj'  => HBKJ::class,
        'ykc'   => YKC::class,
    ];

    /**
     * 查询订单状态
     * QueryOrderToThirdParty constructor.
     * @param array $args
     * @throws Throwable
     */
    public function __construct(array $args)
    {
        if (!isset($args['name_abbreviation'])) {
            throw new Exception("未匹配到任务目标");
        }

        $this->args = $args;
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-04-30 16:00
     */
    public function handle()
    {
        $doHandle = (new $this->handleMapping[$this->args['name_abbreviation']]($this->args));
        $doHandle->handle();
    }
}
