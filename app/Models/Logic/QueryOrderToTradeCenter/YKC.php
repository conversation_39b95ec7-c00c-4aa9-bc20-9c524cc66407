<?php


namespace App\Models\Logic\QueryOrderToTradeCenter;

use App\Console\Commands\PullForYKCDevice;
use App\Jobs\QueryOrderToTradeCenter;
use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\Log\QueueLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Exception;
use Illuminate\Support\Facades\DB;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Request\YKC as YKCRequest;
use App\Models\Logic\Trade\Pay\YKC as YKC_PAY_LOGIC;
use Throwable;

class YKC extends Base
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/3 11:16 上午
     */
    public function handle()
    {
        if (method_exists($this, $this->args['flag'])) {
            QueueLog::handle(
                "ykc query order no support flag",
                $this->args,
                '云快充',
                'query_order_to_trade_center',
                'error'
            );
        }
        DB::beginTransaction();
        try {
            $orderAssocModel = OrderAssocData::getOneOrderByWhereAndLock([
                [
                    'field'    => 'platform_order_id',
                    'operator' => '=',
                    'value'    => $this->args['platform_order_id'],
                ],
                [
                    'field'    => 'platform_name',
                    'operator' => '=',
                    'value'    => $this->args['name_abbreviation'],
                ],
            ], ['*'], true);
            if (!$orderAssocModel or $orderAssocModel->platform_order_status != 2) {
                DB::commit();
                return;
            }
            $continue = $this->{$this->args['flag']}($orderAssocModel);
            if ($continue) {
                $this->retryLaterQueue(
                    new QueryOrderToTradeCenter($this->args),
                    30,
                    "order_query_cycle_ykc_{$this->args['flag']}_{$this->args['platform_order_id']}",
                    3000
                );
            }
            DB::commit();
            return;
        } catch (Throwable $throwable) {
            DB::commit();
            $this->retryLaterQueue(
                new QueryOrderToTradeCenter($this->args),
                30,
                "order_query_cycle_ykc_{$this->args['flag']}_{$this->args['platform_order_id']}",
                3000
            );
            throw $throwable;
        }
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function start(OrderAssocDao $orderInfo)
    {
        $data = YKCRequest::handle('query_equip_charge_status', [
            'StartChargeSeq' => $this->args['platform_order_id'],
        ]);
        if (in_array($data['parsedData']['StartChargeSeqStat'] ?? 0, [1, 2])) {
            FOSS_ORDERRequest::handle('/api/oil_adapter/orderStopElectricity', [
                'order_id'     => $orderInfo->self_order_id,
                'start_result' => 1,
            ]);
            return;
        }
    }

    /**
     * @throws Exception|Throwable
     */
    public function going(OrderAssocDao $orderInfo)
    {
        $data = YKCRequest::handle('query_equip_charge_status', [
            'StartChargeSeq' => $this->args['platform_order_id'],
        ]);
        FOSS_ORDERRequest::handle('/api/oil_adapter/orderGoingElectricity', [
            'order_id'                => $orderInfo->self_order_id,
            'connector_id'            => $data['parsedData']['ConnectorID'],
            'connector_status'        => PullForYKCDevice::getStatusMapping()[$data['parsedData']['ConnectorStatus']],
            'current_a'               => $data['parsedData']['CurrentA'],
            'current_b'               => $data['parsedData']['CurrentB'],
            'current_c'               => $data['parsedData']['CurrentC'],
            'fetch_time'              => $data['parsedData']['EndTime'],
            'remainder_num'           => $data['parsedData']['Soc'],
            'total_money'             => $data['parsedData']['TotalMoney'],
            'total_electricity_money' => $data['parsedData']['ElecMoney'] ?? 0.00,
            'total_service_money'     => $data['parsedData']['SeviceMoney'] ?? 0.00,
            'total_num'               => $data['parsedData']['TotalPower'],
            'voltage_a'               => $data['parsedData']['VoltageA'],
            'voltage_b'               => $data['parsedData']['VoltageB'],
            'voltage_c'               => $data['parsedData']['VoltageC'],
            'price'                   => (empty($data['parsedData']['TotalMoney']) or
                                          empty($data['parsedData']['TotalPower'])) ? 0.00 : bcdiv(
                $data['parsedData']['TotalMoney'],
                $data['parsedData']['TotalPower'],
                2
            ),
        ]);
    }

    /**
     * @throws Exception|Throwable
     */
    public function finish(OrderAssocDao $orderInfo)
    {
        $data = YKCRequest::handle('query_ykc_finish_orders', [
            'StartChargeSeq' => $this->args['platform_order_id'],
        ]);
        $orderDetail = [];
        foreach ($data['parsedData']['ChargeDetails'] as $v) {
            $orderDetail[] = [
                "start_time"              => $v['DetailStartTime'],
                "end_time"                => $v['DetailEndTime'],
                "total_num"               => $v['DetailPower'],
                "price"                   => $v['ElecPrice'],
                "service_price"           => $v['SevicePrice'],
                "total_electricity_money" => $v['DetailElecMoney'],
                "total_service_money"     => $v['DetailSeviceMoney'],
                "total_money"             => bcadd($v['DetailElecMoney'], $v['DetailSeviceMoney'], 2),
            ];
        }
        FOSS_ORDERRequest::handle('/api/oil_adapter/orderFinishElectricity', [
            'order_id'                     => $orderInfo->self_order_id,
            'end_time'                     => $data['parsedData']['EndTime'],
            'stop_reason'                  => YKC_PAY_LOGIC::STOP_REASON_MAPPING[$data['parsedData']['StopReason']] ?? '',
            'num'                          => $data['parsedData']['TotalPower'],
            'money'                        => $data['parsedData']['TotalOriginalMoney'],
            'service_money'                => $data['parsedData']['TotalOriginalSeviceMoney'],
            'electricity_money'            => $data['parsedData']['TotalOriginalElecMoney'],
            'discounted_money'             => $data['parsedData']['TotalMoney'],
            'discounted_service_money'     => $data['parsedData']['TotalSeviceMoney'],
            'discounted_electricity_money' => $data['parsedData']['TotalElecMoney'],
            'order_detail'                 => $orderDetail,
        ]);
        OrderAssocData::updateOrderInfoByOrderId($this->args['platform_order_id'], [
            'platform_order_status' => 1,
        ], true, 'ykc');
    }
}
