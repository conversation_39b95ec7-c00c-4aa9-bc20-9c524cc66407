<?php


namespace App\Models\Logic\Trade\Pay;


use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class YKC extends ThirdParty
{
    public const ACQUIRE_ORDER_LOCK_DURATION_PREFIX = "pay_order_";
    public const ACQUIRE_ORDER_LOCK_DURATION        = 10;

    public const STOP_REASON_MAPPING = [
        0 => '用户手动停止充电 ',
        1 => '客户归属地运营商平台停止充电',
        2 => 'BMS 停止充电',
        3 => '充电机设备故障',
        4 => '连接器断开 ',
    ];

    /**
     * @return Response|void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-31 19:46
     */
    public function handle(): Response
    {
        $acquireCardLockKey = self::ACQUIRE_ORDER_LOCK_DURATION_PREFIX . 'ykc_' . $this->data['StartChargeSeq'];
        $connection = app('redis');
        try {
            if ($connection->set(
                $acquireCardLockKey,
                $this->data['StartChargeSeq'],
                'ex',
                self::ACQUIRE_ORDER_LOCK_DURATION,
                'nx'
            )) {
                $orderAssocDao = OrderAssocData::getOrderInfoByWhere([
                    [
                        'field'    => 'platform_name',
                        'operator' => '=',
                        'value'    => $this->name_abbreviation,
                    ],
                    [
                        'field'    => 'platform_order_id',
                        'operator' => '=',
                        'value'    => $this->data['StartChargeSeq'],
                    ],
                ], ['*'], true, true);
                if (!$orderAssocDao or !$orderAssocDao->exists()) {
                    return responseFormatForYKC(5000018, [
                        'ConfirmResult'  => 9,
                        'ConnectorID'    => $this->data['ConnectorID'],
                        'StartChargeSeq' => $this->data['StartChargeSeq'],
                    ]);
                }
                $orderDetail = [];
                foreach ($this->data['ChargeDetails'] as $v) {
                    $orderDetail[] = [
                        "start_time"              => $v['DetailStartTime'],
                        "end_time"                => $v['DetailEndTime'],
                        "total_num"               => $v['DetailPower'],
                        "price"                   => $v['ElecPrice'],
                        "service_price"           => $v['SevicePrice'],
                        "total_electricity_money" => $v['DetailElecMoney'],
                        "total_service_money"     => $v['DetailSeviceMoney'],
                        "total_money"             => bcadd($v['DetailElecMoney'], $v['DetailSeviceMoney'], 2),
                    ];
                }
                FOSS_ORDERRequest::handle('/api/oil_adapter/orderFinishElectricity', [
                    'order_id'                     => $orderAssocDao->self_order_id,
                    'end_time'                     => $this->data['EndTime'],
                    'stop_reason'                  => self::STOP_REASON_MAPPING[$this->data['StopReason']] ?? '',
                    'num'                          => $this->data['TotalPower'],
                    'money'                        => $this->data['TotalOriginalMoney'],
                    'service_money'                => $this->data['TotalOriginalSeviceMoney'],
                    'electricity_money'            => $this->data['TotalOriginalElecMoney'],
                    'discounted_money'             => $this->data['TotalMoney'],
                    'discounted_service_money'     => $this->data['TotalSeviceMoney'],
                    'discounted_electricity_money' => $this->data['TotalElecMoney'],
                    'order_detail'                 => $orderDetail,
                ]);
                OrderAssocData::updateOrderInfoByOrderId($this->data['realData']['outOrderId'], [
                    'platform_order_status' => 1,
                ], true, $this->name_abbreviation);
                releaseRedisLock($connection, $acquireCardLockKey, $this->data['StartChargeSeq']);
                return responseFormatForYKC(0, [
                    'ConfirmResult'  => 0,
                    'ConnectorID'    => $this->data['ConnectorID'],
                    'StartChargeSeq' => $this->data['StartChargeSeq'],
                ]);
            } else {
                releaseRedisLock($connection, $acquireCardLockKey, $this->data['StartChargeSeq']);
                return responseFormatForYKC(5000100, [
                    'ConfirmResult'  => 9,
                    'ConnectorID'    => $this->data['ConnectorID'],
                    'StartChargeSeq' => $this->data['StartChargeSeq'],
                ]);
            }
        } catch (Throwable $exception) {
            ResponseLog::handle([
                'exception' => $exception,
            ]);
            releaseRedisLock($connection, $acquireCardLockKey, $this->data['StartChargeSeq']);
            return responseFormatForYKC($exception->getCode() == 0 ? 5000001 : $exception->getCode(), [
                'ConfirmResult'  => 9,
                'ConnectorID'    => $this->data['ConnectorID'],
                'StartChargeSeq' => $this->data['StartChargeSeq'],
            ]);
        }
    }
}
