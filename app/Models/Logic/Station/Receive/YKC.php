<?php


namespace App\Models\Logic\Station\Receive;


use Illuminate\Http\JsonResponse;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class YKC extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2019/11/28 3:58 下午
     */
    public function handle(): JsonResponse
    {
        try {
            FOSS_STATIONRequest::handle("v1/charge/pushChargingStatus", [
                'supplier_code' => $this->data['auth_data']['role_code'],
                'connector_id'  => $this->data['ConnectorStatusInfo']['ConnectorID'],
                'status'        => $this->data['ConnectorStatusInfo']['Status'],
                'park_status'   => $this->data['ConnectorStatusInfo']['ParkStatus'] ?? 0,
                'lock_status'   => $this->data['ConnectorStatusInfo']['LockStatus'] ?? 0,
            ]);
            return responseFormatForYKC(0, [
                'Status' => 0
            ]);
        } catch (Throwable $throwable) {
            return responseFormatForYKC(
                $throwable->getCode() == 0 ? 5000001 : $throwable->getCode(),
                [],
                $throwable->getMessage()
            );
        }
    }
}
